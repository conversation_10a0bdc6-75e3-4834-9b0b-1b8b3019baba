#!/usr/bin/env python3
"""
Tool test cho Image Pipeline
Mục đích: Test chức năng tìm kiếm và tạo ảnh mới cho xe từ database

Author: Auto-generated by Serena
Date: 2025-08-13
"""

import logging
import sys
import os
from typing import Optional, Dict, List
import json

# Import các module từ dự án
from config import DB_CONFIG, IMAGE_CONFIG, LOGGING_CONFIG
from database_manager import DatabaseManager
from image_pipeline import fetch_and_process_images_for_car, ImageResult


def setup_logging() -> logging.Logger:
    """Thiết lập logging cho tool test"""
    logging.basicConfig(
        level=getattr(logging, LOGGING_CONFIG['level']),
        format=LOGGING_CONFIG['format'],
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('test_image_tool.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)


def display_car_info(car: Dict) -> None:
    """Hiển thị thông tin xe được chọn"""
    print("\n" + "="*60)
    print("🚗 THÔNG TIN XE ĐƯỢC CHỌN")
    print("="*60)
    print(f"ID xe: {car['car_id']}")
    print(f"Tên xe: {car['car_name']}")
    print(f"Giá xe: {car['car_price']:,} VNĐ")
    print(f"Giá text: {car['car_price_text']}")
    print(f"Link: {car['car_link']}")
    print("="*60)


def display_image_results(results: List[ImageResult]) -> None:
    """Hiển thị kết quả xử lý ảnh"""
    print("\n" + "="*60)
    print("🖼️  KẾT QUẢ XỬ LÝ ẢNH")
    print("="*60)
    
    if not results:
        print("❌ Không có ảnh nào được xử lý thành công!")
        return
    
    print(f"✅ Đã xử lý thành công {len(results)} ảnh:")
    for i, result in enumerate(results, 1):
        print(f"  {i}. URL gốc: {result.url}")
        print(f"     File local: {result.local_path}")
        print(f"     Tồn tại: {'✅' if os.path.exists(result.local_path) else '❌'}")
        print()


def test_single_car(db: DatabaseManager, logger: logging.Logger, 
                   num_images: int = 3) -> Optional[Dict]:
    """
    Test với một xe ngẫu nhiên
    
    Args:
        db: Database manager instance
        logger: Logger instance
        num_images: Số lượng ảnh muốn tạo
        
    Returns:
        Dict chứa thông tin test hoặc None nếu lỗi
    """
    logger.info("🚀 Bắt đầu test với một xe ngẫu nhiên...")
    
    # Lấy xe ngẫu nhiên
    logger.info("📋 Đang lấy xe ngẫu nhiên từ database...")
    car = db.get_random_car()
    
    if not car:
        logger.error("❌ Không thể lấy xe ngẫu nhiên từ database!")
        return None
    
    logger.info(f"✅ Đã chọn xe: {car['car_name']} (ID: {car['car_id']})")
    display_car_info(car)
    
    # Tìm kiếm và xử lý ảnh
    logger.info(f"🔍 Đang tìm kiếm và xử lý {num_images} ảnh cho xe...")
    
    try:
        results = fetch_and_process_images_for_car(car['car_name'], want=num_images)
        logger.info(f"✅ Hoàn thành xử lý ảnh. Kết quả: {len(results)} ảnh")
        
        display_image_results(results)
        
        return {
            'car': car,
            'images': [{'url': r.url, 'local_path': r.local_path} for r in results],
            'success': True,
            'message': f"Thành công xử lý {len(results)} ảnh"
        }
        
    except Exception as e:
        logger.error(f"❌ Lỗi khi xử lý ảnh: {str(e)}")
        return {
            'car': car,
            'images': [],
            'success': False,
            'message': f"Lỗi: {str(e)}"
        }


def test_multiple_cars(db: DatabaseManager, logger: logging.Logger, 
                      num_cars: int = 3, num_images: int = 2) -> List[Dict]:
    """
    Test với nhiều xe
    
    Args:
        db: Database manager instance
        logger: Logger instance
        num_cars: Số lượng xe để test
        num_images: Số lượng ảnh cho mỗi xe
        
    Returns:
        List các kết quả test
    """
    logger.info(f"🚀 Bắt đầu test với {num_cars} xe...")
    results = []
    
    for i in range(num_cars):
        logger.info(f"\n📋 Test xe thứ {i+1}/{num_cars}")
        result = test_single_car(db, logger, num_images)
        if result:
            results.append(result)
        else:
            logger.warning(f"⚠️ Xe thứ {i+1} test thất bại")
    
    return results


def save_test_results(results: List[Dict], filename: str = "test_results.json") -> None:
    """Lưu kết quả test vào file JSON"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"💾 Đã lưu kết quả test vào: {filename}")
    except Exception as e:
        print(f"❌ Lỗi khi lưu kết quả: {e}")


def main():
    """Hàm chính của tool test"""
    logger = setup_logging()
    
    print("🔧 IMAGE PIPELINE TEST TOOL")
    print("="*60)
    print("Tool này sẽ test chức năng tìm kiếm và tạo ảnh cho xe từ database")
    print()
    
    # Kiểm tra cấu hình
    logger.info("🔧 Kiểm tra cấu hình...")
    
    if not IMAGE_CONFIG.get('google_api_key'):
        logger.warning("⚠️ Chưa có Google API Key - có thể ảnh hưởng đến tìm kiếm ảnh")
    
    if not IMAGE_CONFIG.get('google_cx'):
        logger.warning("⚠️ Chưa có Google Custom Search Engine ID")
    
    # Kết nối database
    logger.info("🔌 Đang kết nối database...")
    db = DatabaseManager()
    
    try:
        db.connect()
        logger.info("✅ Kết nối database thành công")
        
        # Kiểm tra số lượng xe trong database
        car_count = db.get_cars_count()
        logger.info(f"📊 Database hiện có {car_count} xe")
        
        if car_count == 0:
            logger.error("❌ Database không có xe nào để test!")
            return
        
        # Menu lựa chọn
        print("\n🎯 CHỌN CHẾ ĐỘ TEST:")
        print("1. Test với 1 xe (3 ảnh)")
        print("2. Test với 3 xe (2 ảnh mỗi xe)")
        print("3. Test tùy chỉnh")
        print("0. Thoát")
        
        choice = input("\nNhập lựa chọn (0-3): ").strip()
        
        if choice == "0":
            print("👋 Tạm biệt!")
            return
        elif choice == "1":
            result = test_single_car(db, logger, num_images=3)
            if result:
                save_test_results([result])
        elif choice == "2":
            results = test_multiple_cars(db, logger, num_cars=3, num_images=2)
            if results:
                save_test_results(results)
        elif choice == "3":
            try:
                num_cars = int(input("Số lượng xe để test: "))
                num_images = int(input("Số lượng ảnh cho mỗi xe: "))
                
                if num_cars <= 0 or num_images <= 0:
                    print("❌ Số lượng phải lớn hơn 0!")
                    return
                
                if num_cars == 1:
                    result = test_single_car(db, logger, num_images)
                    if result:
                        save_test_results([result])
                else:
                    results = test_multiple_cars(db, logger, num_cars, num_images)
                    if results:
                        save_test_results(results)
                        
            except ValueError:
                print("❌ Vui lòng nhập số hợp lệ!")
                return
        else:
            print("❌ Lựa chọn không hợp lệ!")
            return
        
        print("\n✅ Test hoàn thành!")
        
    except Exception as e:
        logger.error(f"❌ Lỗi khi chạy test: {e}")
        
    finally:
        db.disconnect()
        logger.info("🔌 Đã ngắt kết nối database")


if __name__ == "__main__":
    main()