#!/usr/bin/env python3
"""
Script test để kiểm tra tích hợp image_modifier vào image_pipeline
"""

import os
import logging
from image_pipeline import ImageProcessor

def test_image_processing():
    """Test chức năng xử lý ảnh với và không có background removal"""
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Tạo processor
    processor = ImageProcessor(logger)
    
    # Kiểm tra xem có ảnh test không
    test_image_dir = "assets/images"
    if not os.path.exists(test_image_dir):
        logger.error(f"Không tìm thấy thư mục test: {test_image_dir}")
        return
    
    # Tìm ảnh test đầu tiên
    test_image = None
    for root, dirs, files in os.walk(test_image_dir):
        for file in files:
            if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                test_image = os.path.join(root, file)
                break
        if test_image:
            break
    
    if not test_image:
        logger.error("Không tìm thấy ảnh test nào")
        return
    
    logger.info(f"Sử dụng ảnh test: {test_image}")
    
    # Test 1: Với background removal tắt (mặc định)
    logger.info("=== TEST 1: Background removal TẮT ===")
    os.environ['ENABLE_BACKGROUND_REMOVAL'] = 'false'
    
    output1 = "test_output_no_bg_removal.jpg"
    result1 = processor.replace_background(test_image, output1)
    
    if result1:
        logger.info(f"✅ Test 1 thành công: {result1}")
    else:
        logger.error("❌ Test 1 thất bại")
    
    # Test 2: Với background removal bật
    logger.info("=== TEST 2: Background removal BẬT ===")
    os.environ['ENABLE_BACKGROUND_REMOVAL'] = 'true'
    
    output2 = "test_output_with_bg_removal.jpg"
    result2 = processor.replace_background(test_image, output2)
    
    if result2:
        logger.info(f"✅ Test 2 thành công: {result2}")
    else:
        logger.error("❌ Test 2 thất bại")
    
    # Test 3: Sử dụng method apply_image_modifications
    logger.info("=== TEST 3: apply_image_modifications ===")
    
    output3 = "test_output_advanced_modifications.jpg"
    result3 = processor.apply_image_modifications(
        test_image, 
        output3, 
        use_blur=True, 
        use_lsb=True
    )
    
    if result3:
        logger.info(f"✅ Test 3 thành công: {result3}")
    else:
        logger.error("❌ Test 3 thất bại")
    
    # Tóm tắt kết quả
    logger.info("=== TÓM TẮT KẾT QUẢ ===")
    logger.info(f"Test 1 (No BG removal): {'✅' if result1 else '❌'}")
    logger.info(f"Test 2 (With BG removal): {'✅' if result2 else '❌'}")
    logger.info(f"Test 3 (Advanced modifications): {'✅' if result3 else '❌'}")
    
    # Hiển thị thông tin file output
    for i, output in enumerate([output1, output2, output3], 1):
        if os.path.exists(output):
            size = os.path.getsize(output)
            logger.info(f"File test {i}: {output} ({size} bytes)")

if __name__ == "__main__":
    test_image_processing()