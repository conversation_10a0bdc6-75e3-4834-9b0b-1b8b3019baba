"""
<PERSON><PERSON><PERSON> hình cho dự án scraping bonbanh.com
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'database': os.getenv('DB_NAME', 'xehoi_pro'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', 'pwdpwd'),
}

# Scraping configuration
SCRAPING_CONFIG = {
    'base_url': 'https://bonbanh.com/oto/page,{}',
    'delay_between_requests': int(os.getenv('DELAY_BETWEEN_REQUESTS', 2)),
    'max_retries': int(os.getenv('MAX_RETRIES', 3)),
    'timeout': int(os.getenv('TIMEOUT', 30)),
    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'pages_to_scrape': 5,
}

# XPath selectors
XPATH_SELECTORS = {
    'car_items': '//div[contains(@class, "car-item") or contains(@class, "item")]',
    'car_name': './/h3/a/text() | .//h2/a/text() | .//a[contains(@class, "car-name")]/text()',
    'car_price': './/b[@itemprop="price"]/@content | .//span[contains(@class, "price")]/text()',
    'car_id': './/a[contains(@href, "/oto/")]/@href',
    'car_link': './/a[contains(@href, "/oto/")]/@href',
}

# Logging configuration
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'scraper.log'
}


# LLM configuration (OpenAI)
LLM_CONFIG = {
    'provider': 'openai',
    'api_key': os.getenv('OPENAI_API_KEY', ''),
    'model': os.getenv('OPENAI_MODEL', 'gpt-4o-mini'),
    'temperature': float(os.getenv('OPENAI_TEMPERATURE', 0.7)),
    'max_tokens': int(os.getenv('OPENAI_MAX_TOKENS', 1800)),
}


# Image configuration
IMAGE_CONFIG = {
    'provider_priority': ['google'],  # ưu tiên theo thứ tự
    'google_api_key': os.getenv('GOOGLE_API_KEY', ''),
    'google_cx': os.getenv('GOOGLE_CX', ''),  # Custom Search Engine ID
    'download_timeout': int(os.getenv('IMG_DOWNLOAD_TIMEOUT', 20)),
    'per_car_images': int(os.getenv('PER_CAR_IMAGES', 5)),
    'assets_root': os.getenv('ASSETS_ROOT', 'assets'),
    'backgrounds_dir': os.getenv('BACKGROUNDS_DIR', 'assets/backgrounds'),
    'watermark_text': os.getenv('WATERMARK_TEXT', 'xehoi.pro'),
}
