#!/usr/bin/env python3
"""
Dự án thu thập dữ liệu xe ô tô từ bonbanh.com
"""
import logging
import sys
from datetime import datetime
from car_scraper import CarScraper
from database_manager import DatabaseManager
from config import SCRAPING_CONFIG, LOGGING_CONFIG


def setup_logging():
    """Thiết lập logging"""
    logging.basicConfig(
        level=getattr(logging, LOGGING_CONFIG['level']),
        format=LOGGING_CONFIG['format'],
        handlers=[
            logging.FileHandler(LOGGING_CONFIG['file'], encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def main():
    """Hàm chính để chạy scraper"""
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("=" * 60)
    logger.info("BẮT ĐẦU THU THẬP DỮ LIỆU XE TỪ BONBANH.COM")
    logger.info(f"Thời gian: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 60)

    # Khởi tạo database manager
    db_manager = DatabaseManager()

    try:
        # Kết nối database
        if not db_manager.connect():
            logger.error("Không thể kết nối database. Thoát chương trình.")
            return False

        # Tạo bảng nếu chưa có
        if not db_manager.create_cars_table():
            logger.error("Không thể tạo bảng cars. Thoát chương trình.")
            return False

        # Khởi tạo scraper
        scraper = CarScraper()

        # Scrape dữ liệu từ 5 trang đầu
        pages_to_scrape = SCRAPING_CONFIG['pages_to_scrape']
        logger.info(f"Bắt đầu scrape {pages_to_scrape} trang đầu tiên")

        all_cars = scraper.scrape_multiple_pages(1, pages_to_scrape)

        if not all_cars:
            logger.warning("Không thu thập được dữ liệu xe nào")
            return False

        logger.info(f"Đã thu thập tổng cộng {len(all_cars)} xe")

        # Lưu vào database
        logger.info("Bắt đầu lưu dữ liệu vào database...")
        inserted_count = db_manager.insert_cars_batch(all_cars)

        logger.info(f"Đã lưu {inserted_count} xe vào database")

        # Thống kê
        total_cars = db_manager.get_cars_count()
        logger.info(f"Tổng số xe trong database: {total_cars}")

        logger.info("=" * 60)
        logger.info("HOÀN THÀNH THU THẬP DỮ LIỆU")
        logger.info("=" * 60)

        return True

    except Exception as e:
        logger.error(f"Lỗi trong quá trình thực hiện: {e}")
        return False

    finally:
        # Đóng kết nối database
        db_manager.disconnect()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
