# Hướng dẫn sử dụng Test Image Tool

## Tổng quan

Dự án này cung cấp 2 tool để test chức năng tìm kiếm và tạo ảnh cho xe:

1. **`test_image_tool.py`** - Tool đầy đủ với menu và logging chi tiết
2. **`quick_test_image.py`** - Tool đơn giản để test nhanh

## Y<PERSON>u cầu hệ thống

### 1. Cài đặt dependencies
```powershell
# Kích hoạt virtual environment
.\venv\Scripts\Activate.ps1

# Cài đặt packages (nếu chưa có)
pip install -r requirements.txt
```

### 2. Cấu hình môi trường
Đảm bảo file `.env` có các thông tin cần thiết:

```env
# Database
DB_HOST=localhost
DB_NAME=xehoi_pro
DB_USER=root
DB_PASSWORD=your_password

# Google API (cho tìm kiếm ảnh)
GOOGLE_API_KEY=your_google_api_key
GOOGLE_CX=your_custom_search_engine_id
```

### 3. <PERSON><PERSON>m tra database
```powershell
# Kiểm tra kết nối và dữ liệu
python check_database.py
```

## Cách sử dụng

### Tool đơn giản (Khuyến nghị cho test nhanh)

```powershell
python quick_test_image.py
```

**Chức năng:**
- Lấy ngẫu nhiên 1 xe từ database
- Tìm kiếm và xử lý 2 ảnh cho xe đó
- Hiển thị kết quả đơn giản

### Tool đầy đủ

```powershell
python test_image_tool.py
```

**Menu lựa chọn:**
1. **Test với 1 xe (3 ảnh)** - Test chi tiết với 1 xe
2. **Test với 3 xe (2 ảnh mỗi xe)** - Test batch với nhiều xe
3. **Test tùy chỉnh** - Tự định nghĩa số xe và số ảnh
0. **Thoát**

## Kết quả mong đợi

### Thành công
```
🚗 Xe được chọn: Toyota Camry 2020
💰 Giá: 1,200,000,000 VNĐ

🔍 Đang tìm kiếm ảnh cho: Toyota Camry 2020

✅ Kết quả: 2 ảnh được xử lý
  1. assets/toyota-camry-2020/final/01.jpg
  2. assets/toyota-camry-2020/final/02.jpg
```

### Cấu trúc thư mục ảnh được tạo
```
assets/
└── toyota-camry-2020/
    ├── raw/           # Ảnh gốc tải về
    │   ├── raw_001.jpg
    │   └── raw_002.jpg
    └── final/         # Ảnh đã xử lý (background + watermark)
        ├── 01.jpg
        └── 02.jpg
```

## Troubleshooting

### Lỗi thường gặp

1. **Không kết nối được database**
   ```
   ❌ Lỗi: Access denied for user 'root'@'localhost'
   ```
   - Kiểm tra thông tin database trong `.env`
   - Đảm bảo MySQL server đang chạy

2. **Không có Google API Key**
   ```
   ⚠️ Chưa có Google API Key - có thể ảnh hưởng đến tìm kiếm ảnh
   ```
   - Cần có Google API Key để tìm kiếm ảnh
   - Xem hướng dẫn tại: [Google Custom Search API](https://developers.google.com/custom-search/v1/introduction)

3. **Database trống**
   ```
   ❌ Database không có xe nào để test!
   ```
   - Chạy scraper để thu thập dữ liệu: `python main.py`

4. **Lỗi xử lý ảnh**
   ```
   ❌ Lỗi xử lý ảnh: [Errno 2] No such file or directory
   ```
   - Kiểm tra quyền ghi thư mục `assets/`
   - Đảm bảo có kết nối internet để tải ảnh

### Debug

1. **Kiểm tra log chi tiết**
   ```powershell
   # Xem log của tool test
   Get-Content test_image_tool.log -Tail 20
   
   # Xem log chung của hệ thống
   Get-Content scraper.log -Tail 20
   ```

2. **Test từng bước**
   ```python
   # Test kết nối database
   from database_manager import DatabaseManager
   db = DatabaseManager()
   db.connect()
   car = db.get_random_car()
   print(car)
   
   # Test tìm kiếm ảnh
   from image_pipeline import fetch_and_process_images_for_car
   results = fetch_and_process_images_for_car("Toyota Camry", want=1)
   print(results)
   ```

## Tích hợp với hệ thống

### Sử dụng trong code khác

```python
from test_image_tool import test_single_car
from database_manager import DatabaseManager
import logging

# Setup
logger = logging.getLogger(__name__)
db = DatabaseManager()
db.connect()

# Test
result = test_single_car(db, logger, num_images=3)
if result['success']:
    print(f"Thành công: {result['message']}")
    for img in result['images']:
        print(f"Ảnh: {img['local_path']}")
```

### Tự động hóa

```powershell
# Chạy test hàng ngày
python quick_test_image.py >> daily_test.log 2>&1
```

## Mở rộng

Tool có thể được mở rộng để:
- Test với các loại xe cụ thể
- Test với các từ khóa tìm kiếm khác nhau
- Đánh giá chất lượng ảnh
- So sánh hiệu suất các API tìm kiếm khác nhau
- Tích hợp với CI/CD pipeline