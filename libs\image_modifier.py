
import cv2
import numpy as np
from PIL import Image, ImageEnhance
import os
import random

def modify_image(input_path, output_path=None):
    """
    Chỉnh sửa ảnh để tránh phát hiện trùng lặp từ Google
    
    Args:
        input_path (str): Đường dẫn ảnh gốc
        output_path (str): Đường dẫn ảnh output (tùy chọn)
    
    Returns:
        str: Đường dẫn ảnh đã chỉnh sửa
    """
    
    # Tạo tên file output nếu không được cung cấp
    if output_path is None:
        base_name = os.path.splitext(input_path)[0]
        output_path = f"{base_name}_modified.jpg"
    
    # 1. Mở ảnh bằng PIL
    with Image.open(input_path) as img:
        
        # 2. Resize ảnh (thay đổi kích thước ngẫu nhiên 5-15%)
        original_size = img.size
        resize_factor = random.uniform(0.85, 1.15)  # Tăng/giảm 5-15%
        new_size = (
            int(original_size[0] * resize_factor),
            int(original_size[1] * resize_factor)
        )
        img_resized = img.resize(new_size, Image.LANCZOS)
        
        # 3. Flip horizontal (tạo mirror effect)
        img_flipped = img_resized.transpose(Image.FLIP_LEFT_RIGHT)
        
        # 4. Điều chỉnh color balance và saturation
        # Điều chỉnh độ bão hòa màu
        saturation_enhancer = ImageEnhance.Color(img_flipped)
        saturation_factor = random.uniform(0.9, 1.1)  # Thay đổi 10%
        img_saturated = saturation_enhancer.enhance(saturation_factor)
        
        # Điều chỉnh độ sáng
        brightness_enhancer = ImageEnhance.Brightness(img_saturated)
        brightness_factor = random.uniform(0.95, 1.05)  # Thay đổi 5%
        img_bright = brightness_enhancer.enhance(brightness_factor)
        
        # Điều chỉnh độ tương phản
        contrast_enhancer = ImageEnhance.Contrast(img_bright)
        contrast_factor = random.uniform(0.95, 1.05)  # Thay đổi 5%
        img_contrast = contrast_enhancer.enhance(contrast_factor)
        
        # 5. Thêm subtle noise
        # Chuyển sang numpy array để thêm noise
        img_array = np.array(img_contrast)
        
        # Thêm Gaussian noise nhẹ
        noise_strength = random.uniform(1, 3)  # Noise rất nhẹ
        noise = np.random.normal(0, noise_strength, img_array.shape)
        img_noisy = img_array + noise
        
        # Đảm bảo giá trị pixel trong khoảng 0-255
        img_noisy = np.clip(img_noisy, 0, 255).astype(np.uint8)
        
        # Chuyển lại về PIL Image
        img_final = Image.fromarray(img_noisy)
        
        # 6. Thay đổi format và loại bỏ metadata
        # Loại bỏ metadata bằng cách tạo ảnh mới
        data = list(img_final.getdata())
        img_no_metadata = Image.new(img_final.mode, img_final.size)
        img_no_metadata.putdata(data)
        
        # Lưu với format mới (JPEG) và quality cao
        img_no_metadata.save(output_path, 'JPEG', quality=95, optimize=True)
        
        print(f"Ảnh đã được chỉnh sửa thành công!")
        print(f"Kích thước gốc: {original_size}")
        print(f"Kích thước mới: {new_size}")
        print(f"Đã lưu tại: {output_path}")
        
        return output_path

def add_gaussian_blur(input_path, output_path=None, blur_strength=1):
    """
    Thêm Gaussian blur nhẹ vào ảnh
    
    Args:
        input_path (str): Đường dẫn ảnh input
        output_path (str): Đường dẫn ảnh output
        blur_strength (int): Độ mạnh của blur (1-5)
    """
    
    if output_path is None:
        base_name = os.path.splitext(input_path)[0]
        output_path = f"{base_name}_blurred.jpg"
    
    # Đọc ảnh bằng OpenCV
    img = cv2.imread(input_path)
    
    # Áp dụng Gaussian blur
    kernel_size = blur_strength * 2 + 1  # Kernel size phải là số lẻ
    blurred = cv2.GaussianBlur(img, (kernel_size, kernel_size), 0)
    
    # Lưu ảnh
    cv2.imwrite(output_path, blurred)
    print(f"Đã thêm blur và lưu tại: {output_path}")
    
    return output_path

def modify_lsb_steganography(input_path, output_path=None):
    """
    Chỉnh sửa LSB (Least Significant Bit) để thay đổi digital fingerprint
    
    Args:
        input_path (str): Đường dẫn ảnh input
        output_path (str): Đường dẫn ảnh output
    """
    
    if output_path is None:
        base_name = os.path.splitext(input_path)[0]
        output_path = f"{base_name}_lsb_modified.jpg"
    
    # Đọc ảnh
    img = cv2.imread(input_path)
    height, width, channels = img.shape
    
    # Thay đổi ngẫu nhiên LSB của một số pixel
    modification_rate = 0.1  # Thay đổi 10% pixel
    num_pixels_to_modify = int(height * width * modification_rate)
    
    for _ in range(num_pixels_to_modify):
        # Chọn pixel ngẫu nhiên
        y = random.randint(0, height - 1)
        x = random.randint(0, width - 1)
        channel = random.randint(0, channels - 1)
        
        # Thay đổi LSB
        original_value = img[y, x, channel]
        if original_value % 2 == 0:
            img[y, x, channel] = original_value + 1
        else:
            img[y, x, channel] = original_value - 1
    
    # Lưu ảnh
    cv2.imwrite(output_path, img)
    print(f"Đã chỉnh sửa LSB và lưu tại: {output_path}")
    
    return output_path

def batch_process_images(input_folder, output_folder=None):
    """
    Xử lý hàng loạt ảnh trong thư mục
    
    Args:
        input_folder (str): Thư mục chứa ảnh gốc
        output_folder (str): Thư mục lưu ảnh đã chỉnh sửa
    """
    
    if output_folder is None:
        output_folder = input_folder + "_modified"
    
    # Tạo thư mục output nếu chưa tồn tại
    os.makedirs(output_folder, exist_ok=True)
    
    # Các định dạng ảnh được hỗ trợ
    supported_formats = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff')
    
    for filename in os.listdir(input_folder):
        if filename.lower().endswith(supported_formats):
            input_path = os.path.join(input_folder, filename)
            output_filename = os.path.splitext(filename)[0] + "_modified.jpg"
            output_path = os.path.join(output_folder, output_filename)
            
            try:
                modify_image(input_path, output_path)
            except Exception as e:
                print(f"Lỗi xử lý {filename}: {e}")

# Ví dụ sử dụng:
if __name__ == "__main__":
    # Chỉnh sửa một ảnh đơn lẻ
    # modify_image("input_image.jpg", "output_image.jpg")
    
    # Xử lý hàng loạt
    # batch_process_images("input_folder", "output_folder")
    
    print("Script đã sẵn sàng!")
    print("Sử dụng:")
    print("1. modify_image('path/to/image.jpg') - chỉnh sửa một ảnh")
    print("2. add_gaussian_blur('path/to/image.jpg') - thêm blur")
    print("3. modify_lsb_steganography('path/to/image.jpg') - chỉnh sửa LSB")
    print("4. batch_process_images('folder_path') - xử lý hàng loạt")