# Test Tools cho Image Pipeline

## Quick Start

### 1. Test nhanh (Khuy<PERSON><PERSON> nghị)
```powershell
python quick_test_image.py
```

### 2. Test offline (Không cần Google API)
```powershell
python test_image_offline.py
```

### 3. Test đầy đủ với menu
```powershell
python test_image_tool.py
```

### 4. <PERSON><PERSON><PERSON> tra hệ thống
```powershell
python check_dependencies.py
```

## Yêu cầu
- Database MySQL đã có dữ liệu xe
- File `.env` đã cấu hình đúng
- Google API Key (tùy chọn, cho tìm kiếm ảnh thật)

## Kết quả
- Ảnh được lưu trong thư mục `assets/`
- Log chi tiết trong `test_image_tool.log`
- Kết quả JSON trong `test_results.json`

## Status: ✅ HOẠT ĐỘNG
- <PERSON><PERSON> sửa lỗi `download_images_async`
- <PERSON><PERSON> sửa lỗi `textsize` deprecated
- Đ<PERSON> tạo ảnh nền mẫu
- Test thành công với ảnh offline

## Hướng dẫn chi tiết
Xem file: `docs/test_image_tool_guide.md`