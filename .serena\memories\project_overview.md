# Tổng quan dự án xehoi_pro_create_post_6

## Mục đích dự án
Dự án thu thập dữ liệu xe ô tô từ website bonbanh.com và lưu trữ vào cơ sở dữ liệu MySQL. Mục tiêu là:
- Thu thập thông tin xe từ 5 trang đầu tiên của bonbanh.com
- Trích xuất dữ liệu: tên xe, gi<PERSON> xe, ID xe
- Lưu trữ dữ liệu vào MySQL database

## Tech Stack
- **Ngôn ngữ**: Python 3
- **Web Scraping**: requests, beautifulsoup4, lxml, selenium
- **Database**: MySQL (mysql-connector-python)
- **Utilities**: python-dotenv, pandas, webdriver-manager

## Cấu trúc dự án
```
xehoi_pro_create_post_6/
├── .serena/
│   └── project.yml
├── .env.example
├── main.py
├── requirements.txt
└── memory_bank/ (sẽ tạo)
```

## Môi trường phát triển
- Hệ điều hành: Windows
- Python environment với các dependencies trong requirements.txt