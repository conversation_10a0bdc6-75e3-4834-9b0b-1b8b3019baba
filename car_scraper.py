"""
Car Scraper cho việc thu thập dữ liệu xe từ bonbanh.com
"""
import requests
from lxml import html
import time
import logging
import re
from typing import List, Dict, Optional
from urllib.parse import urljoin, urlparse
from config import SCRAPING_CONFIG, XPATH_SELECTORS


class CarScraper:
    """Scraper để thu thập dữ liệu xe từ bonbanh.com"""

    def __init__(self):
        """Khởi tạo CarScraper"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': SCRAPING_CONFIG['user_agent']
        })
        self.logger = logging.getLogger(__name__)
        self.base_url = SCRAPING_CONFIG['base_url']
        self.delay = SCRAPING_CONFIG['delay_between_requests']
        self.max_retries = SCRAPING_CONFIG['max_retries']
        self.timeout = SCRAPING_CONFIG['timeout']

    def fetch_page(self, url: str, retries: int = 0) -> Optional[str]:
        """
        <PERSON><PERSON>y nội dung HTML từ URL

        Args:
            url (str): URL cần fetch
            retries (int): Số lần retry hiện tại

        Returns:
            Optional[str]: Nội dung HTML hoặc None nếu thất bại
        """
        try:
            self.logger.info(f"Đang fetch: {url}")
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()

            # Kiểm tra content type
            if 'text/html' not in response.headers.get('content-type', ''):
                self.logger.warning(f"Content type không phải HTML: {url}")
                return None

            return response.text

        except requests.exceptions.RequestException as e:
            self.logger.error(f"Lỗi fetch {url}: {e}")

            if retries < self.max_retries:
                wait_time = (retries + 1) * 2
                self.logger.info(f"Retry {retries + 1}/{self.max_retries} sau {wait_time}s")
                time.sleep(wait_time)
                return self.fetch_page(url, retries + 1)

            return None

    def extract_car_id(self, car_link: str) -> Optional[str]:
        """
        Trích xuất ID xe từ link

        Args:
            car_link (str): Link đến trang chi tiết xe

        Returns:
            Optional[str]: ID xe hoặc None
        """
        if not car_link:
            return None

        # Tìm pattern như /oto/mã-xe-số hoặc /oto/số
        patterns = [
            r'/oto/([^/]+)',
            r'ma,(\d+)',
            r'id=(\d+)',
        ]

        for pattern in patterns:
            match = re.search(pattern, car_link)
            if match:
                return match.group(1)

        # Fallback: sử dụng toàn bộ path
        parsed = urlparse(car_link)
        if parsed.path:
            return parsed.path.strip('/').replace('/', '_')

        return None

    def parse_price(self, price_text: str) -> tuple:
        """
        Parse giá xe từ text

        Args:
            price_text (str): Text chứa giá xe

        Returns:
            tuple: (giá_số, giá_text)
        """
        if not price_text:
            return None, None

        # Làm sạch text
        price_text = price_text.strip()

        # Tìm số trong text
        numbers = re.findall(r'[\d,\.]+', price_text)
        if not numbers:
            return None, price_text

        try:
            # Xử lý các format giá phổ biến
            if 'tỷ' in price_text.lower() or 'ty' in price_text.lower():
                # Format: "3 Tỷ 890 Triệu"
                if len(numbers) >= 2:
                    ty = float(numbers[0].replace(',', '.'))
                    trieu = float(numbers[1].replace(',', '.'))
                    price_value = int((ty * 1000 + trieu) * 1000000)
                else:
                    ty = float(numbers[0].replace(',', '.'))
                    price_value = int(ty * 1000000000)
            elif 'triệu' in price_text.lower() or 'trieu' in price_text.lower():
                # Format: "890 Triệu"
                trieu = float(numbers[0].replace(',', '.'))
                price_value = int(trieu * 1000000)
            else:
                # Thử parse trực tiếp
                price_value = int(numbers[0].replace(',', '').replace('.', ''))

            return price_value, price_text

        except (ValueError, IndexError) as e:
            self.logger.warning(f"Không thể parse giá: {price_text} - {e}")
            return None, price_text

    def extract_car_data(self, element, page_number: int) -> Optional[Dict]:
        """
        Trích xuất dữ liệu xe từ HTML element

        Args:
            element: HTML element chứa thông tin xe
            page_number (int): Số trang

        Returns:
            Optional[Dict]: Dữ liệu xe hoặc None
        """
        try:
            # Tìm tên xe từ h3 trong cb2_02
            car_name = None
            name_elements = element.xpath('.//h3[@itemprop="name"]/text()')
            if name_elements:
                car_name = name_elements[0].strip()

            if not car_name:
                # Fallback: tìm trong các h3 khác
                name_elements = element.xpath('.//h3/text()')
                if name_elements:
                    car_name = name_elements[0].strip()

            if not car_name:
                return None

            # Tìm giá xe
            price_value = None
            price_text = None

            # Thử tìm giá từ attribute content
            price_elements = element.xpath('.//b[@itemprop="price"]/@content')
            if price_elements:
                try:
                    price_value = int(price_elements[0])
                    # Tìm text hiển thị
                    price_display = element.xpath('.//b[@itemprop="price"]/text()')
                    price_text = price_display[0] if price_display else str(price_value)
                except (ValueError, IndexError):
                    pass

            # Nếu không tìm thấy, thử các selector khác
            if price_value is None:
                price_selectors = [
                    './/span[contains(@class, "price")]/text()',
                    './/div[contains(@class, "price")]/text()',
                    './/b[contains(text(), "Tỷ") or contains(text(), "Triệu")]/text()',
                ]

                for selector in price_selectors:
                    prices = element.xpath(selector)
                    if prices:
                        price_value, price_text = self.parse_price(prices[0])
                        if price_value:
                            break

            # Tìm link xe từ attribute itemprop="url"
            car_link = None
            link_elements = element.xpath('.//a[@itemprop="url"]/@href')
            if link_elements:
                car_link = urljoin('https://bonbanh.com/', link_elements[0])

            if not car_link:
                # Fallback: tìm link xe thông thường
                link_elements = element.xpath('.//a[contains(@href, "/oto/")]/@href')
                if link_elements:
                    car_link = urljoin('https://bonbanh.com/', link_elements[0])

            # Trích xuất ID xe
            car_id = self.extract_car_id(car_link) if car_link else None
            if not car_id:
                # Fallback: tạo ID từ tên xe
                car_id = re.sub(r'[^\w\s-]', '', car_name).strip()
                car_id = re.sub(r'[-\s]+', '-', car_id)[:50]

            return {
                'car_id': car_id,
                'car_name': car_name,
                'car_price': price_value,
                'car_price_text': price_text,
                'car_link': car_link,
                'page_number': page_number
            }

        except Exception as e:
            self.logger.error(f"Lỗi extract car data: {e}")
            return None

    def parse_cars_from_html(self, html_content: str, page_number: int) -> List[Dict]:
        """
        Parse dữ liệu xe từ HTML

        Args:
            html_content (str): Nội dung HTML
            page_number (int): Số trang

        Returns:
            List[Dict]: Danh sách thông tin xe
        """
        cars = []

        try:
            tree = html.fromstring(html_content)

            # Tìm các element chứa thông tin xe
            # Dựa vào cấu trúc HTML thực tế: li có class "car-item"
            car_elements = tree.xpath('//li[@class="car-item row1" or @class="car-item row2"]')

            if not car_elements:
                # Fallback: tìm tất cả li có class car-item
                car_elements = tree.xpath('//li[contains(@class, "car-item")]')

            if not car_elements:
                # Fallback: tìm theo giá xe
                car_elements = tree.xpath('//div[.//b[@itemprop="price"]]')

            self.logger.info(f"Tìm thấy {len(car_elements)} xe trên trang {page_number}")

            for element in car_elements:
                car_data = self.extract_car_data(element, page_number)
                if car_data and car_data.get('car_name'):
                    cars.append(car_data)

        except Exception as e:
            self.logger.error(f"Lỗi parse HTML trang {page_number}: {e}")

        return cars

    def scrape_page(self, page_number: int) -> List[Dict]:
        """
        Scrape một trang

        Args:
            page_number (int): Số trang cần scrape

        Returns:
            List[Dict]: Danh sách xe từ trang này
        """
        url = self.base_url.format(page_number)

        # Delay giữa các request
        if page_number > 1:
            time.sleep(self.delay)

        html_content = self.fetch_page(url)
        if not html_content:
            self.logger.error(f"Không thể lấy nội dung trang {page_number}")
            return []

        cars = self.parse_cars_from_html(html_content, page_number)
        self.logger.info(f"Đã scrape {len(cars)} xe từ trang {page_number}")

        return cars

    def scrape_multiple_pages(self, start_page: int = 1, end_page: int = 5) -> List[Dict]:
        """
        Scrape nhiều trang

        Args:
            start_page (int): Trang bắt đầu
            end_page (int): Trang kết thúc

        Returns:
            List[Dict]: Danh sách tất cả xe
        """
        all_cars = []

        for page_num in range(start_page, end_page + 1):
            self.logger.info(f"Đang scrape trang {page_num}/{end_page}")

            cars = self.scrape_page(page_num)
            all_cars.extend(cars)

            self.logger.info(f"Tổng cộng đã thu thập {len(all_cars)} xe")

        return all_cars
