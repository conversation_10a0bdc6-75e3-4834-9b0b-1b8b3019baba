# Hướng dẫn tích hợp xử lý hình ảnh ô tô trong compare\_cars.py

## Mục tiêu

Mở rộng tính năng xử lý hình ảnh tự động trong công cụ so sánh xe `compare_cars.py`:

1. <PERSON><PERSON><PERSON> kiếm 4-5 ảnh chất lượng cao cho mỗi xe.
2. Xử lý background: <PERSON><PERSON> nền cũ bằng background mới chóng vi phạm bản quyền.
3. Chèn ảnh vào bài viết markdown đúng vị trí.
4. Lưu trữ và ghi đường dẫn vào database.

---

## 1. Công nghệ & API được sử dụng

### Tìm kiếm ảnh:

- **Bing Image Search API** (Microsoft Azure) – độ tin cậy cao, có license info

### Loại & thay background:

- ``: Loại background local không cần API.
- ``** / **``: <PERSON><PERSON> nền mới sau khi xoá background.

---

## 2. Cấu trúc file mới

```
project/
├── compare_cars.py
├── config.py
├── database.py
├── image/
│   ├── downloader.py       # Tìm & tải ảnh
│   ├── processor.py        # Xoá & thay background
│   ├── inserter.py         # Chèn ảnh vào Markdown
```

---

## 3. Cài đặt dependencies

```
pip install requests rembg pillow markdown
```

`config.py`

```python
BING_API_KEY = "your_key"
BACKGROUND_IMAGE_PATH = "assets/bg_default.jpg"
```

---

## 4. Downloader: Tìm & tải ảnh

**image/downloader.py**

```python
import requests, os
from typing import List
from config import BING_API_KEY

def search_images(query: str, count: int = 5) -> List[str]:
    url = "https://api.bing.microsoft.com/v7.0/images/search"
    headers = {"Ocp-Apim-Subscription-Key": BING_API_KEY}
    params = {"q": query, "count": count, "imageType": "Photo"}
    r = requests.get(url, headers=headers, params=params)
    return [img["contentUrl"] for img in r.json().get("value", [])]

def download_images(urls: List[str], save_dir: str, prefix: str) -> List[str]:
    os.makedirs(save_dir, exist_ok=True)
    paths = []
    for i, url in enumerate(urls):
        try:
            r = requests.get(url, timeout=10)
            if r.status_code == 200:
                path = f"{save_dir}/{prefix}_{i}.jpg"
                with open(path, "wb") as f:
                    f.write(r.content)
                paths.append(path)
        except: continue
    return paths
```

---

## 5. Processor: Loại & thay background

**image/processor.py**

```python
from rembg import remove
from PIL import Image
import os
from config import BACKGROUND_IMAGE_PATH

def replace_background(input_path: str, output_path: str):
    with open(input_path, "rb") as f:
        fg_bytes = remove(f.read())

    fg = Image.open(io.BytesIO(fg_bytes)).convert("RGBA")
    bg = Image.open(BACKGROUND_IMAGE_PATH).convert("RGBA").resize(fg.size)

    composite = Image.alpha_composite(bg, fg)
    composite.save(output_path)
    return output_path
```

---

## 6. Inserter: Thêm ảnh vào Markdown

**image/inserter.py**

```python
from typing import List

def insert_images_to_md(md: str, image_paths: List[str]) -> str:
    parts = md.split("\n\n")
    parts.insert(1, "\n".join(f"![Ảnh xe]({p})" for p in image_paths[:2]))
    for i, p in enumerate(parts):
        if "| Thông số" in p:
            parts.insert(i+1, f"![Chi tiết]({image_paths[2]})")
            break
    parts.append(f"![Tổng quan]({image_paths[-1]})")
    return "\n\n".join(parts)
```

---

## 7. Tích hợp vào `compare_cars.py`

```python
from image.downloader import search_images, download_images
from image.processor import replace_background
from image.inserter import insert_images_to_md

urls = search_images("Toyota Vios 2024")
raw_imgs = download_images(urls, "images/Toyota_Vios", "vios")
final_imgs = [replace_background(p, p.replace(".jpg", "_final.png")) for p in raw_imgs]

with open("output/article.md", "r", encoding="utf-8") as f:
    content = f.read()

new_md = insert_images_to_md(content, final_imgs)

with open("output/article_final.md", "w", encoding="utf-8") as f:
    f.write(new_md)
```

---

## 8. Ghi đường dẫn vào database

```python
def update_image_paths(car_id: str, image_paths: List[str]):
    # Code ghi DB theo cấu trúc hiện có
    pass
```

---

## 9. Kiểm thử & Hiệu suất

- Test đối với nhiều mã xe (Mazda, Honda, VinFast...)
- Log lỗi khi không tìm được ảnh
- Lưu cache ảnh đã tên tránh tìm lại
- Sử dụng async cho downloader (tùy chọn)

---

## 10. Lưu ý về bản quyền & license

- Dùng Bing API và filter theo `license=Any` hoặc `ShareCommercially`
- Background thay thế nên dùng tự thiết kế hoặc mua background bối stock
- Tên file ảnh: khuyên dùng theo công thức `car_slug_idx_final.png`

---

## 11. Gợi ý background thay thế

- Nền garage/nhà xưởng blur
- Nền asphalt road + bokeh
- Nền gradient xám/trắng
- Lấy trong thư mục `assets/backgrounds/`

---

## Kết luận

Tính năng xử lý background và tìm ảnh tự động giúc bài viết chên ảnh đẹp và chuyên nghiệp hơn. Code được chia module rõ ràng, dễ test và scale.

Bạn có thể triển khai tính năng này ngay trong `compare_cars.py` và tổ chức pipeline theo quy trình:

> Tìm ảnh → Xử lý → Chèn markdown → Lưu database

---

