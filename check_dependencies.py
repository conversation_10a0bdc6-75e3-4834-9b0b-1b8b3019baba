#!/usr/bin/env python3
"""
Kiểm tra dependencies và cấu hình cho test tools
"""

import sys
import os
from pathlib import Path

def check_python_version():
    """Kiểm tra phiên bản Python"""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("⚠️  Khuyến nghị Python 3.8+")
    else:
        print("✅ Python version OK")

def check_modules():
    """Kiểm tra các module cần thiết"""
    required_modules = [
        'mysql.connector',
        'requests', 
        'PIL',
        'asyncio',
        'logging',
        'json',
        'dotenv'
    ]
    
    print("\n📦 Kiểm tra modules:")
    missing = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - THIẾU")
            missing.append(module)
    
    if missing:
        print(f"\n⚠️  Cần cài đặt: {', '.join(missing)}")
        print("Chạy: pip install -r requirements.txt")
    else:
        print("\n✅ Tất cả modules đã có")

def check_project_files():
    """Kiểm tra các file dự án cần thiết"""
    required_files = [
        'config.py',
        'database_manager.py', 
        'image_pipeline.py',
        'test_image_tool.py',
        'quick_test_image.py',
        '.env'
    ]
    
    print("\n📁 Kiểm tra files dự án:")
    missing = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - THIẾU")
            missing.append(file)
    
    if missing:
        print(f"\n⚠️  Files thiếu: {', '.join(missing)}")
    else:
        print("\n✅ Tất cả files dự án đã có")

def check_env_config():
    """Kiểm tra cấu hình .env"""
    print("\n⚙️  Kiểm tra cấu hình .env:")
    
    if not os.path.exists('.env'):
        print("❌ File .env không tồn tại")
        print("💡 Copy từ .env.example: copy .env.example .env")
        return
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        required_vars = ['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD']
        optional_vars = ['GOOGLE_API_KEY', 'GOOGLE_CX']
        
        for var in required_vars:
            value = os.getenv(var)
            if value:
                print(f"✅ {var}: {'*' * len(value)}")
            else:
                print(f"❌ {var}: THIẾU")
        
        print("\n🔧 Cấu hình tùy chọn:")
        for var in optional_vars:
            value = os.getenv(var)
            if value:
                print(f"✅ {var}: {'*' * min(len(value), 10)}")
            else:
                print(f"⚠️  {var}: Chưa có (ảnh hưởng tìm kiếm ảnh)")
                
    except Exception as e:
        print(f"❌ Lỗi đọc .env: {e}")

def check_directories():
    """Kiểm tra thư mục cần thiết"""
    print("\n📂 Kiểm tra thư mục:")
    
    dirs = ['assets', 'assets/backgrounds', 'docs', 'memory_bank']
    
    for dir_path in dirs:
        if os.path.exists(dir_path):
            print(f"✅ {dir_path}")
        else:
            print(f"⚠️  {dir_path} - sẽ tự tạo khi cần")

def main():
    print("🔍 KIỂM TRA DEPENDENCIES VÀ CẤU HÌNH")
    print("="*50)
    
    check_python_version()
    check_modules()
    check_project_files()
    check_env_config()
    check_directories()
    
    print("\n" + "="*50)
    print("🎯 HƯỚNG DẪN TIẾP THEO:")
    print("1. Nếu có lỗi, sửa theo gợi ý ở trên")
    print("2. Chạy test nhanh: python quick_test_image.py")
    print("3. Hoặc test đầy đủ: python test_image_tool.py")
    print("4. Xem hướng dẫn: docs/test_image_tool_guide.md")

if __name__ == "__main__":
    main()