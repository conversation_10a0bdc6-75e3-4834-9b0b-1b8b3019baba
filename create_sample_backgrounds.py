#!/usr/bin/env python3
"""
Tạo ảnh nền mẫu cho image pipeline
"""

import os
from PIL import Image, ImageDraw

def create_gradient_background(width=1920, height=1080, color1=(135, 206, 235), color2=(25, 25, 112)):
    """Tạo ảnh nền gradient"""
    image = Image.new('RGB', (width, height))
    draw = ImageDraw.Draw(image)
    
    for y in range(height):
        # Tính toán màu gradient
        ratio = y / height
        r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
        g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
        b = int(color1[2] * (1 - ratio) + color2[2] * ratio)
        
        draw.line([(0, y), (width, y)], fill=(r, g, b))
    
    return image

def create_solid_background(width=1920, height=1080, color=(240, 240, 240)):
    """Tạo ảnh nền màu đơn"""
    return Image.new('RGB', (width, height), color)

def main():
    """Tạo các ảnh nền mẫu"""
    bg_dir = "assets/backgrounds"
    os.makedirs(bg_dir, exist_ok=True)
    
    print("🎨 Đang tạo ảnh nền mẫu...")
    
    # Tạo các ảnh nền khác nhau
    backgrounds = [
        ("bg_gradient_blue.jpg", create_gradient_background(1920, 1080, (135, 206, 235), (25, 25, 112))),
        ("bg_gradient_sunset.jpg", create_gradient_background(1920, 1080, (255, 94, 77), (255, 154, 0))),
        ("bg_solid_white.jpg", create_solid_background(1920, 1080, (255, 255, 255))),
        ("bg_solid_gray.jpg", create_solid_background(1920, 1080, (240, 240, 240))),
        ("bg_gradient_green.jpg", create_gradient_background(1920, 1080, (144, 238, 144), (34, 139, 34))),
    ]
    
    for filename, image in backgrounds:
        filepath = os.path.join(bg_dir, filename)
        image.save(filepath, "JPEG", quality=85)
        print(f"✅ Đã tạo: {filepath}")
    
    print(f"\n🎉 Hoàn thành! Đã tạo {len(backgrounds)} ảnh nền trong {bg_dir}")

if __name__ == "__main__":
    main()
