# Workflow: <PERSON>hu thập dữ liệu xe từ bonbanh.com

## M<PERSON><PERSON> tiêu

Thu thập dữ liệu xe ô tô từ 5 trang đầu tiên của bonbanh.com và lưu vào MySQL database.

## C<PERSON><PERSON> bước thực hiện

### 1. Thiết lập môi trường

- [x] Tạo cấu trúc dự án
- [x] Thiết lập requirements.txt
- [x] Tạo file .env.example
- [x] Tạo file .env với thông tin database thực tế
- [x] Cài đặt dependencies

### 2. Phân tích website bonbanh.com

- [x] Kiểm tra robots.txt
- [x] Phân tích cấu trúc HTML của trang danh sách xe
- [x] Xác định XPath cho các elements cần thu thập
- [x] Test với 1 trang trước khi scale

### 3. Ph<PERSON>t triển scraper

- [x] Tạo class CarScraper
- [x] Implement method để fetch HTML từ URL
- [x] Implement method để parse dữ liệu xe từ HTML
- [x] Implement retry logic và error handling
- [x] Add logging và progress tracking

### 4. Thiết lập database

- [x] Tạo class DatabaseManager
- [x] Thiết kế schema cho bảng cars
- [x] Implement connection management
- [x] Implement CRUD operations

### 5. Integration và testing

- [x] Kết hợp scraper với database
- [x] Test với 1 trang
- [x] Test với 5 trang
- [x] Verify dữ liệu trong database

### 6. Optimization và cleanup

- [x] Implement rate limiting
- [x] Add data validation
- [x] Error recovery mechanisms
- [x] Final testing và documentation

## Kết quả đạt được

### Thống kê thu thập dữ liệu

- **Tổng số trang đã scrape**: 5 trang
- **Tổng số xe thu thập được**: 100 xe
- **Số xe lưu vào database**: 99 xe (1 xe trùng lặp)
- **Thời gian thực hiện**: ~10 giây

### Dữ liệu thu thập được

Mỗi xe bao gồm các thông tin:

- **car_id**: Mã định danh duy nhất
- **car_name**: Tên xe (ví dụ: "Mercedes Benz S class S450 - 2021")
- **car_price**: Giá xe dạng số (ví dụ: 3890000000)
- **car_price_text**: Giá xe dạng text (ví dụ: "3 Tỷ 890 Triệu")
- **car_link**: Link đến trang chi tiết xe
- **page_number**: Số trang được scrape
- **scraped_at**: Thời gian thu thập

### Công nghệ sử dụng

- **Python 3** với các thư viện: requests, lxml, mysql-connector-python
- **MySQL Database** để lưu trữ dữ liệu
- **XPath** để trích xuất dữ liệu từ HTML
- **Retry logic** và **rate limiting** để đảm bảo ổn định

### Files được tạo

- `main.py`: Script chính để chạy scraper
- `car_scraper.py`: Class CarScraper để thu thập dữ liệu
- `database_manager.py`: Class DatabaseManager để quản lý database
- `config.py`: Cấu hình cho dự án
- `test_scraper.py`: Script test scraper
- `requirements.txt`: Dependencies của dự án
