#!/usr/bin/env python3
"""
Test script để kiểm tra scraper
"""
import logging
import sys
from car_scraper import CarScraper

def setup_logging():
    """Thiết lập logging cho test"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )

def test_scraper():
    """Test scraper với 1 trang"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("Bắt đầu test scraper...")
    
    scraper = CarScraper()
    
    # Test với trang 1
    cars = scraper.scrape_page(1)
    
    logger.info(f"Đã scrape {len(cars)} xe từ trang 1")
    
    # In ra 3 xe đầu tiên để kiểm tra
    for i, car in enumerate(cars[:3]):
        logger.info(f"Xe {i+1}:")
        logger.info(f"  ID: {car.get('car_id')}")
        logger.info(f"  Tên: {car.get('car_name')}")
        logger.info(f"  Giá: {car.get('car_price')} ({car.get('car_price_text')})")
        logger.info(f"  Link: {car.get('car_link')}")
        logger.info("-" * 50)
    
    return len(cars) > 0

if __name__ == "__main__":
    success = test_scraper()
    print(f"Test {'THÀNH CÔNG' if success else 'THẤT BẠI'}")
    sys.exit(0 if success else 1)
