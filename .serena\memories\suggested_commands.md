# <PERSON><PERSON><PERSON> lệnh quan trọng cho dự án

## Thiết lập môi trường
```powershell
# Tạo virtual environment
python -m venv venv

# Kích hoạt virtual environment (Windows)
.\venv\Scripts\Activate.ps1

# Cài đặt dependencies
pip install -r requirements.txt

# Tạo file .env từ template
copy .env.example .env
```

## Ch<PERSON>y dự án
```powershell
# Chạy script chính
python main.py

# Chạy với Python interpreter
python -i main.py
```

## Lệnh hệ thống Windows
```powershell
# Liệt kê files/folders
dir
Get-ChildItem

# Tìm kiếm files
Get-ChildItem -Recurse -Filter "*.py"

# Xem nội dung file
Get-Content filename.txt
type filename.txt

# Tạo thư mục
mkdir folder_name
New-Item -ItemType Directory -Name folder_name

# Copy file
copy source.txt destination.txt
Copy-Item source.txt destination.txt
```

## Database
```powershell
# Kết nối MySQL (cần cài MySQL client)
mysql -h localhost -u username -p database_name
```

## Git commands
```powershell
git status
git add .
git commit -m "message"
git push
git pull
```