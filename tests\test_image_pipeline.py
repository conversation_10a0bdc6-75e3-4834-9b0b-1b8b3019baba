import os
import unittest
from PIL import Image

from image_pipeline import ImageProcessor, insert_images_into_markdown, ImageResult


class TestImagePipeline(unittest.TestCase):
    def setUp(self):
        self.proc = ImageProcessor()
        os.makedirs('tmp', exist_ok=True)
        # T<PERSON><PERSON>nh giả lập 1280x720
        self.sample_path = 'tmp/sample.jpg'
        img = Image.new('RGB', (1280, 720), color=(200, 200, 200))
        img.save(self.sample_path)

    def tearDown(self):
        try:
            os.remove(self.sample_path)
        except Exception:
            pass

    def test_score_image(self):
        with Image.open(self.sample_path) as im:
            score = self.proc.score_image(im)
        self.assertGreater(score, 0.2)

    def test_replace_background_and_watermark(self):
        out_path = 'tmp/out.jpg'
        res = self.proc.replace_background(self.sample_path, out_path)
        # <PERSON><PERSON> thể fail nếu chưa có backgrounds, nhưng code fallback nền trắng
        self.assertTrue(res is None or os.path.exists(out_path))

    def test_insert_images_into_markdown(self):
        md = "# Tiêu đề\n\nNội dung."
        imgs = [ImageResult(url='u', local_path='p.jpg')]
        out = insert_images_into_markdown(md, imgs)
        self.assertIn('![Hình mở bài](p.jpg)', out)


if __name__ == '__main__':
    unittest.main()

