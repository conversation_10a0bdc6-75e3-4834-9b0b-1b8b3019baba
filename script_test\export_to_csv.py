#!/usr/bin/env python3
"""
Script để export dữ liệu xe ra file CSV
"""
import csv
import logging
import sys
from datetime import datetime
from database_manager import DatabaseManager

def setup_logging():
    """Thiết lập logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )

def export_to_csv():
    """Export dữ liệu xe ra CSV"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    db_manager = DatabaseManager()
    
    try:
        # Kết nối database
        if not db_manager.connect():
            logger.error("Không thể kết nối database")
            return False
        
        # Lấy tất cả dữ liệu xe
        query = """
        SELECT car_id, car_name, car_price, car_price_text, car_link, page_number, scraped_at
        FROM cars 
        ORDER BY page_number, id
        """
        db_manager.cursor.execute(query)
        results = db_manager.cursor.fetchall()
        
        if not results:
            logger.warning("Không có dữ liệu để export")
            return False
        
        # Tạo tên file với timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"bonbanh_cars_{timestamp}.csv"
        
        # Ghi ra file CSV
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # Header
            writer.writerow([
                'Car ID', 'Car Name', 'Price (VND)', 'Price Text', 
                'Link', 'Page Number', 'Scraped At'
            ])
            
            # Data
            for row in results:
                writer.writerow(row)
        
        logger.info(f"Đã export {len(results)} xe ra file: {filename}")
        return True
        
    except Exception as e:
        logger.error(f"Lỗi export CSV: {e}")
        return False
        
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    success = export_to_csv()
    sys.exit(0 if success else 1)
