# Workflow: Tạo <PERSON>l Test Image Pipeline

## Mục tiêu
Tạ<PERSON> một script/tool Python mới có chức năng:
- Lấy ngẫu nhiên một xe từ database hiện có
- Sử dụng thư viện `image_pipeline.py` để tìm kiếm và tạo ảnh mới cho xe đó
- Mục đích: test và kiểm tra chức năng tìm kiếm/tạo ảnh mới từ ảnh tìm kiếm

## Phân tích code hiện có

### File `compare_cars.py`
- Chứa function `pick_two_cars()` để lấy ngẫu nhiên xe từ database
- Sử dụng `DatabaseManager` để truy cập database

### File `image_pipeline.py`
- Chứa function `fetch_and_process_images_for_car()` để tìm kiếm và xử lý ảnh
- Class `ImageSearcher` để tìm kiếm ảnh
- Class `ImageProcessor` để xử lý ảnh (background, watermark)

### File `database_manager.py`
- Class `DatabaseManager` với method `get_random_car()` để lấy xe ngẫu nhiên
- Kết nối MySQL database với config từ file `.env`

## Kế hoạch thực hiện

### Bước 1: Tạo tool test chính
- File: `test_image_tool.py`
- Chức năng:
  - Import các module cần thiết
  - Kết nối database
  - Lấy ngẫu nhiên 1 xe
  - Gọi image pipeline để tạo ảnh
  - Logging chi tiết quá trình

### Bước 2: Tích hợp với hệ thống hiện có
- Sử dụng config từ `config.py`
- Sử dụng `DatabaseManager` từ `database_manager.py`
- Sử dụng `fetch_and_process_images_for_car` từ `image_pipeline.py`

### Bước 3: Logging và output
- Setup logging để theo dõi quá trình
- Output thông tin xe được chọn
- Output kết quả tìm kiếm và xử lý ảnh

## Trạng thái
- [x] Phân tích code hiện có
- [x] Tạo tool test chính
- [x] Tạo hướng dẫn sử dụng
- [x] Test và debug
- [x] Sửa lỗi và hoàn thiện

## Files đã tạo
1. **test_image_tool.py** - Tool test đầy đủ với menu và logging chi tiết
2. **quick_test_image.py** - Tool test nhanh và đơn giản
3. **test_image_offline.py** - Tool test offline không cần Google API
4. **docs/test_image_tool_guide.md** - Hướng dẫn sử dụng chi tiết
5. **create_sample_backgrounds.py** - Script tạo ảnh nền mẫu
6. **check_dependencies.py** - Script kiểm tra dependencies
7. **TEST_TOOLS_README.md** - Hướng dẫn nhanh

## Chức năng đã implement
- Lấy xe ngẫu nhiên từ database
- Tích hợp với image_pipeline.py để tìm kiếm và xử lý ảnh
- Logging chi tiết quá trình xử lý
- Menu lựa chọn cho các chế độ test khác nhau
- Lưu kết quả test vào file JSON
- Hiển thị thông tin xe và kết quả ảnh
- Test offline với ảnh tự tạo (không cần Google API)
- Tạo ảnh nền mẫu tự động
- Kiểm tra dependencies và cấu hình

## Lỗi đã sửa
1. **Lỗi chính**: `'ImageProcessor' object has no attribute 'download_images_async'`
   - Nguyên nhân: Method `download_images_async` và `score_image` nằm sai class
   - Giải pháp: Di chuyển các method từ `ImageSearchError` vào `ImageProcessor`

2. **Lỗi PIL**: `'ImageDraw' object has no attribute 'textsize'`
   - Nguyên nhân: Method `textsize` đã deprecated trong PIL mới
   - Giải pháp: Sử dụng `textbbox` với fallback cho phiên bản cũ

3. **Thiếu ảnh nền**: Warning không có ảnh nền
   - Giải pháp: Tạo script `create_sample_backgrounds.py` để tạo ảnh nền mẫu

## Test Results
✅ **Tool hoạt động thành công**:
- Database connection: OK
- Lấy xe ngẫu nhiên: OK
- Xử lý ảnh (score_image): OK
- Background replacement: OK
- Watermark: OK
- Tạo ảnh final: OK

⚠️ **Lưu ý**: Để test với ảnh thật từ internet cần cấu hình Google API Key trong file `.env`