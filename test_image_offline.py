#!/usr/bin/env python3
"""
Test Image Pipeline offline - không cần Google API
Tạo ảnh test và xử lý background + watermark
"""

import logging
import os
import sys
from PIL import Image, ImageDraw
from database_manager import DatabaseManager
from image_pipeline import ImageProcessor


def create_test_car_image(car_name: str, save_path: str) -> str:
    """Tạo ảnh xe test đơn giản"""
    # Tạo ảnh test 800x600
    img = Image.new('RGB', (800, 600), color=(70, 130, 180))  # Steel blue
    draw = ImageDraw.Draw(img)
    
    # Vẽ hình chữ nhật đại diện cho xe
    car_rect = [150, 200, 650, 400]
    draw.rectangle(car_rect, fill=(220, 220, 220), outline=(0, 0, 0), width=3)
    
    # Vẽ bánh xe
    wheel_positions = [(200, 350), (600, 350)]
    for x, y in wheel_positions:
        draw.ellipse([x-30, y-30, x+30, y+30], fill=(50, 50, 50), outline=(0, 0, 0), width=2)
        draw.ellipse([x-15, y-15, x+15, y+15], fill=(100, 100, 100))
    
    # Vẽ cửa sổ
    windows = [[200, 220, 350, 280], [450, 220, 600, 280]]
    for window in windows:
        draw.rectangle(window, fill=(135, 206, 235), outline=(0, 0, 0), width=2)
    
    # Viết tên xe
    try:
        from PIL import ImageFont
        try:
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            font = ImageFont.load_default()
        
        # Tính toán vị trí text
        text = car_name[:30] + "..." if len(car_name) > 30 else car_name
        bbox = draw.textbbox((0, 0), text, font=font)
        text_w = bbox[2] - bbox[0]
        text_x = (800 - text_w) // 2
        
        draw.text((text_x, 100), text, fill=(255, 255, 255), font=font)
    except Exception:
        draw.text((300, 100), "Test Car", fill=(255, 255, 255))
    
    # Lưu ảnh
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    img.save(save_path, 'JPEG', quality=90)
    return save_path


def test_image_processing_offline():
    """Test xử lý ảnh offline"""
    print("🔧 TEST IMAGE PROCESSING OFFLINE")
    print("="*50)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    logger = logging.getLogger(__name__)
    
    # Kết nối database
    db = DatabaseManager()
    
    try:
        db.connect()
        print("✅ Kết nối database thành công")
        
        # Lấy xe ngẫu nhiên
        car = db.get_random_car()
        if not car:
            print("❌ Không thể lấy xe từ database!")
            return
        
        print(f"\n🚗 Xe được chọn: {car['car_name']}")
        print(f"💰 Giá: {car['car_price']:,} VNĐ")
        
        # Tạo thư mục test
        car_dir = f"assets/test/{car['car_name'].replace(' ', '-').replace('/', '-')}"
        raw_dir = os.path.join(car_dir, 'raw')
        final_dir = os.path.join(car_dir, 'final')
        
        os.makedirs(raw_dir, exist_ok=True)
        os.makedirs(final_dir, exist_ok=True)
        
        # Tạo 3 ảnh test
        test_images = []
        for i in range(3):
            raw_path = os.path.join(raw_dir, f"test_{i+1:02d}.jpg")
            create_test_car_image(f"{car['car_name']} - Test {i+1}", raw_path)
            test_images.append(raw_path)
            print(f"✅ Đã tạo ảnh test: {raw_path}")
        
        # Xử lý ảnh với ImageProcessor
        processor = ImageProcessor(logger)
        processed_images = []
        
        print(f"\n🎨 Đang xử lý {len(test_images)} ảnh...")
        
        for i, raw_path in enumerate(test_images):
            final_path = os.path.join(final_dir, f"{i+1:02d}.jpg")
            
            # Test score_image
            try:
                with Image.open(raw_path) as img:
                    score = processor.score_image(img)
                    print(f"📊 Điểm ảnh {i+1}: {score:.2f}")
            except Exception as e:
                print(f"⚠️ Lỗi chấm điểm ảnh {i+1}: {e}")
            
            # Test replace_background
            try:
                processed = processor.replace_background(raw_path, final_path)
                if processed:
                    processed_images.append(processed)
                    print(f"✅ Đã xử lý ảnh {i+1}: {final_path}")
                else:
                    print(f"❌ Lỗi xử lý ảnh {i+1}")
            except Exception as e:
                print(f"❌ Lỗi xử lý ảnh {i+1}: {e}")
        
        # Kết quả
        print(f"\n🎉 KẾT QUẢ:")
        print(f"📁 Thư mục: {car_dir}")
        print(f"🖼️ Ảnh gốc: {len(test_images)}")
        print(f"✨ Ảnh đã xử lý: {len(processed_images)}")
        
        if processed_images:
            print("\n📋 Danh sách ảnh đã xử lý:")
            for img_path in processed_images:
                size = os.path.getsize(img_path) if os.path.exists(img_path) else 0
                print(f"  - {img_path} ({size:,} bytes)")
        
        return len(processed_images) > 0
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False
        
    finally:
        db.disconnect()
        print("\n🔌 Đã ngắt kết nối database")


def main():
    """Hàm chính"""
    success = test_image_processing_offline()
    
    if success:
        print("\n✅ TEST THÀNH CÔNG!")
        print("💡 Chức năng xử lý ảnh hoạt động bình thường")
        print("📝 Để test với ảnh thật, cần cấu hình Google API trong .env")
    else:
        print("\n❌ TEST THẤT BẠI!")
        print("🔧 Kiểm tra lại cấu hình và dependencies")


if __name__ == "__main__":
    main()
