# Quy ước code và style

## Python Style Guidelines
- Tuân thủ PEP 8 cho Python code style
- Sử dụng UTF-8 encoding cho tất cả files
- Docstrings theo format Google style hoặc NumPy style
- Type hints khi có thể

## Naming Conventions
- **Functions/Variables**: snake_case (ví dụ: get_car_data, car_price)
- **Classes**: PascalCase (ví dụ: CarScraper, DatabaseManager)
- **Constants**: UPPER_SNAKE_CASE (ví dụ: MAX_RETRIES, DB_HOST)
- **Files**: snake_case.py (ví dụ: car_scraper.py, database_manager.py)

## Code Organization
- Một class per file khi có thể
- Import statements ở đầu file
- Sử dụng relative imports trong package
- Tách logic thành các modules riêng biệt

## Documentation
- Docstrings cho tất cả functions và classes
- Comments cho logic phức tạp
- README.md cho hướng dẫn sử dụng

## Error Handling
- Sử dụng try-except blocks cho external calls
- Log errors với thông tin chi tiết
- Graceful degradation khi có thể

## Environment Variables
- Sử dụng .env file cho configuration
- Không commit sensitive data
- Provide .env.example template