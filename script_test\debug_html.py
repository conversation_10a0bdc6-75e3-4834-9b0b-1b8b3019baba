#!/usr/bin/env python3
"""
Debug script để phân tích cấu trúc HTML
"""
import requests
from lxml import html
import logging


def debug_html():
    """Debug HTML structure"""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    url = "https://bonbanh.com/oto/page,1"

    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    })

    response = session.get(url)
    tree = html.fromstring(response.text)

    # Tìm tất cả links xe
    car_links = tree.xpath('//a[contains(@href, "/oto/")]')
    logger.info(f"Tìm thấy {len(car_links)} links xe")

    for i, link in enumerate(car_links[:5]):
        if link.text and link.text.strip():
            logger.info(f"Link {i+1}:")
            logger.info(f"  Text: {link.text.strip()}")
            logger.info(f"  Href: {link.get('href')}")
            logger.info(f"  Parent tag: {link.getparent().tag}")
            logger.info(f"  Parent class: {link.getparent().get('class')}")
            logger.info("-" * 30)

    logger.info("=" * 60)

    # Thử tìm element chứa cả tên và giá
    # Tìm element có chứa cả link xe và giá xe
    containers = tree.xpath('//div[.//a[contains(@href, "/oto/")] and .//b[@itemprop="price"]]')
    logger.info(f"Tìm thấy {len(containers)} containers chứa cả tên và giá")

    for i, container in enumerate(containers[:3]):
        logger.info(f"Container {i+1}:")
        logger.info(f"  Tag: {container.tag}, class: {container.get('class')}")

        # Tìm tên xe
        links = container.xpath('.//a[contains(@href, "/oto/")]')
        for link in links:
            if link.text and link.text.strip():
                logger.info(f"  Tên xe: {link.text.strip()}")
                logger.info(f"  Link: {link.get('href')}")
                break

        # Tìm giá xe
        price_elem = container.xpath('.//b[@itemprop="price"]')
        if price_elem:
            logger.info(f"  Giá: {price_elem[0].text} ({price_elem[0].get('content')})")

        logger.info("-" * 50)


if __name__ == "__main__":
    debug_html()
