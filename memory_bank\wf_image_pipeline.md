# Workflow: <PERSON><PERSON> lý ảnh tự động cho bài viết so sánh xe

## M<PERSON><PERSON> tiêu
- Tìm 4-5 ảnh chất lượng cao cho mỗi xe
- Xử lý background để giảm rủi ro bản quyền (remove bg + dán nền miễn phí)
- Ch<PERSON><PERSON> ảnh vào nội dung Markdown ở vị trí hợp lý
- Lưu ảnh local và cập nhật đường dẫn trong DB

## Thành phần
- `image_pipeline.py`
  - `ImageSearcher` (Pexels API, có thể mở rộng DDG/Bing)
  - `ImageProcessor` (tải ảnh, xử lý background)
  - `fetch_and_process_images_for_car` (đầu-cuối cho 1 xe)
  - `insert_images_into_markdown` (chèn ảnh vào nội dung)
- `database_manager.py`
  - B<PERSON> sung cột `image_paths JSON` trong `car_comparisons`
  - `update_comparison_images(comparison_id, image_paths)`
- <PERSON><PERSON><PERSON> hình trong `config.IMAGE_CONFIG` + `.env`

## Cấu hình & Dependencies
- `.env` b<PERSON> sung các biến:
  - `PEXELS_API_KEY`
  - `IMG_DOWNLOAD_TIMEOUT`, `PER_CAR_IMAGES`, `ASSETS_ROOT`, `BACKGROUNDS_DIR`
- Cài packages (PowerShell):
  - pip install pillow requests
  - (Tuỳ chọn) pip install rembg duckduckgo-search

## Quy trình tích hợp (đề xuất)
1. Trong `compare_cars.py` sau khi lưu bài viết lấy `comp_id`, gọi pipeline ảnh cho cả 2 xe.
2. Gộp danh sách ảnh (ưu tiên 2-3 ảnh xe A, 2-3 ảnh xe B) -> chèn vào Markdown bằng `insert_images_into_markdown`.
3. Cập nhật lại nội dung trong DB (nếu muốn) và lưu `image_paths` (JSON) bằng `update_comparison_images`.

## Kiểm thử
- Unit test tách rời từng phần (searcher, processor, insert md)
- Smoke test end-to-end trên 1 cặp xe
- Kiểm tra log và nội dung file trong `assets/images/...`

## Lưu ý bản quyền & rate limiting
- Pexels: ảnh miễn phí, tuân theo license, nên ghi credit nếu yêu cầu
- Tôn trọng rate limit, thêm `sleep` ngắn giữa lượt tải
- Ưu tiên nguồn hợp pháp (Pexels/Unsplash) thay vì scraping kết quả search trực tiếp

## Cải tiến tương lai
- Dùng `rembg` để tách nền và dán lên background ngẫu nhiên trong `assets/backgrounds`
- Áp dụng cache theo URL (hash nội dung) để tránh tải lại
- Tải ảnh bất đồng bộ (aiohttp) để tăng tốc

