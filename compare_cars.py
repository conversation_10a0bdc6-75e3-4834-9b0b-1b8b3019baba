"""
Công cụ tạo bài viết so sánh hai xe từ dữ liệu trong DB và OpenAI API
"""
import logging
import sys
from datetime import datetime
from typing import Optional, Tuple

from config import LOGGING_CONFIG, LLM_CONFIG, IMAGE_CONFIG
from database_manager import DatabaseManager

# OpenAI SDK hiện tại (2024-2025)
try:
    from openai import OpenAI
except Exception:  # fallback nếu gói cũ
    OpenAI = None


def setup_logging():
    logging.basicConfig(
        level=getattr(logging, LOGGING_CONFIG['level']),
        format=LOGGING_CONFIG['format'],
        handlers=[
            logging.FileHandler(LOGGING_CONFIG['file'], encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def pick_two_cars(db: DatabaseManager, tolerance: float = 0.2) -> Optional[Tuple[dict, dict]]:
    """Chọn ngẫu nhiên 2 xe theo tiêu chí: xe2 có giá trong ±20% của xe1"""
    logger = logging.getLogger(__name__)
    car1 = db.get_random_car()
    if not car1:
        logger.warning("Không tìm được xe thứ nhất")
        return None
    car2 = db.get_random_car_by_price_range(car1['car_price'], tolerance=tolerance, exclude_car_id=car1['car_id'])
    if not car2:
        logger.warning("Không tìm được xe thứ hai phù hợp giá ±%.0f%%" % (tolerance * 100))
        return None
    return car1, car2


def build_prompt(car1: dict, car2: dict) -> str:
    """Tạo prompt chi tiết để sinh bài viết so sánh"""
    def car_brief(c: dict) -> str:
        return f"{c.get('car_name')} (Giá: {c.get('car_price_text') or c.get('car_price')} VND) - Link: {c.get('car_link') or 'N/A'}"

    prompt = f"""
Bạn là chuyên gia ô tô tại Việt Nam. Viết một bài so sánh tự nhiên, chuẩn SEO, tránh văn phong máy móc, giữa 2 mẫu xe sau:
- Xe A: {car_brief(car1)}
- Xe B: {car_brief(car2)}

Yêu cầu:
1) Mở bài: bối cảnh, đối tượng phù hợp, nêu tiêu chí so sánh.
2) Tổng quan nhanh từng xe: thương hiệu, phân khúc, thế mạnh thường được nhắc tới.
3) Bảng so sánh thông số kỹ thuật dưới dạng bảng Markdown với các hàng gợi ý: Giá tham khảo, Động cơ/hộp số, Công suất/mô-men xoắn, Mức tiêu hao nhiên liệu ước tính, Kích thước (DxRxC, chiều dài cơ sở), Khoảng sáng gầm, Trang bị an toàn chủ động/bị động, Tiện nghi nổi bật, Năm sản xuất/đời, Số km (nếu có). Nếu thiếu dữ liệu, ghi "—".
4) Phân tích ưu - nhược điểm mỗi xe theo góc nhìn người dùng tại VN (chi phí nuôi xe, độ bền, bán lại, dịch vụ, phụ tùng...).
5) So sánh cảm giác lái, cách âm, thoải mái hàng ghế, công nghệ hỗ trợ lái (nếu có, nêu theo mức độ phổ biến).
6) Kết luận: khuyến nghị rõ ràng theo từng nhóm khách hàng (gia đình đô thị, chạy dịch vụ, đam mê lái xe, tiết kiệm nhiên liệu...).
7) Viết tự nhiên, có ví dụ đời sống; tránh rập khuôn; cấu trúc rõ ràng với các tiêu đề phụ (##, ###); dùng tiếng Việt.
8) Độ dài ~1000-1400 từ.
"""
    return prompt.strip()


def generate_article(car1: dict, car2: dict) -> str:
    logger = logging.getLogger(__name__)
    if LLM_CONFIG.get('provider') != 'openai' or not LLM_CONFIG.get('api_key'):
        raise RuntimeError("Chưa cấu hình OpenAI API key (OPENAI_API_KEY)")

    if OpenAI is None:
        raise RuntimeError("Chưa cài gói openai >= 1.0.0. Hãy chạy: pip install openai")

    client = OpenAI(api_key=LLM_CONFIG['api_key'])

    system_msg = "Bạn là biên tập viên ô tô dày dạn kinh nghiệm, viết đúng ngữ cảnh thị trường Việt Nam."
    user_msg = build_prompt(car1, car2)

    try:
        # Sử dụng Responses API mới của OpenAI nếu khả dụng
        try:
            resp = client.responses.create(
                model=LLM_CONFIG['model'],
                # temperature=LLM_CONFIG['temperature'],
                # max_output_tokens=LLM_CONFIG['max_tokens'],
                input=[
                    {"role": "system", "content": system_msg},
                    {"role": "user", "content": user_msg},
                ],
            )
            # Trích xuất text
            content = ""
            if hasattr(resp, 'output') and resp.output:
                for item in resp.output:
                    if getattr(item, 'type', '') == 'message' and item.content:
                        for block in item.content:
                            if getattr(block, 'type', '') == 'output_text':
                                content += block.text
            if not content:
                # Fallback một số phiên bản SDK
                content = getattr(resp, 'output_text', '') or str(resp)
        except Exception:
            # Fallback sang Chat Completions
            chat = client.chat.completions.create(
                model=LLM_CONFIG['model'],
                # temperature=LLM_CONFIG['temperature'],
                # max_tokens=LLM_CONFIG['max_tokens'],
                messages=[
                    {"role": "system", "content": system_msg},
                    {"role": "user", "content": user_msg},
                ],
            )
            content = chat.choices[0].message.content

        if not content:
            raise RuntimeError("Không nhận được nội dung từ OpenAI")

        return content
    except Exception as e:
        logger.error(f"Lỗi gọi OpenAI: {e}")
        raise


def create_and_save_comparison(max_attempts: int = 8) -> bool:
    logger = logging.getLogger(__name__)
    db = DatabaseManager()

    try:
        if not db.connect():
            logger.error("Không thể kết nối database")
            return False

        # Đảm bảo có bảng lưu bài viết
        if not db.create_comparisons_table():
            logger.error("Không thể tạo bảng car_comparisons")
            return False

        # Lặp chọn cặp xe mới nếu đã có bài viết
        attempt = 0
        while attempt < max_attempts:
            attempt += 1
            picked = pick_two_cars(db, tolerance=0.2)
            if not picked:
                logger.warning(f"[Thử {attempt}/{max_attempts}] Không chọn được cặp xe phù hợp")
                continue
            car1, car2 = picked
            logger.info(f"[Thử {attempt}/{max_attempts}] Cặp xe: '{car1['car_name']}' vs '{car2['car_name']}'")

            if db.comparison_exists(car1['car_name'], car2['car_name']):
                logger.info("Cặp xe này đã có bài viết so sánh, chọn lại...")
                continue

            # Sinh bài viết
            content = generate_article(car1, car2)

            # Lưu
            comp_id = db.save_comparison(car1_name=car1['car_name'], car2_name=car2['car_name'], content=content)
            if not comp_id:
                logger.error("Lưu bài viết thất bại, thử lại cặp khác...")
                continue

            # Xử lý ảnh cho 2 xe (không để fail chặn bài viết)
            try:
                from image_pipeline import (
                    fetch_and_process_images_for_car,
                    insert_images_into_markdown,
                    ImageResult,
                )
                want = IMAGE_CONFIG.get('per_car_images', 5)
                imgs1 = fetch_and_process_images_for_car(car1['car_name'], want=want)
                imgs2 = fetch_and_process_images_for_car(car2['car_name'], want=want)
                # Gộp 4-5 ảnh: ưu tiên 2-3 ảnh xe A + 2-3 ảnh xe B
                merged: list[ImageResult] = []
                merged.extend(imgs1[:max(2, want//2)])
                merged.extend(imgs2[:max(2, want//2)])
                merged = merged[:max(4, min(5, want))]

                if merged:
                    # Cập nhật content có chèn ảnh
                    content_with_images = insert_images_into_markdown(content, merged)
                    # Cập nhật content vào DB (lưu nội dung có ảnh chèn)
                    db.update_comparison_content(comparison_id=comp_id, content=content_with_images)
                    # Cập nhật image_paths vào DB
                    db.update_comparison_images(comparison_id=comp_id, image_paths=[i.local_path for i in merged])
            except Exception as e:
                logger.warning(f"Xử lý ảnh gặp lỗi nhưng không ảnh hưởng bài viết: {e}")

            logger.info(f"Tạo và lưu bài viết so sánh thành công (ID={comp_id})")
            return True

        logger.error("Hết lượt thử nhưng chưa tạo được bài viết mới")
        return False
    except Exception as e:
        logger.error(f"Lỗi quy trình tạo bài viết: {e}")
        return False
    finally:
        db.disconnect()


if __name__ == "__main__":
    setup_logging()
    ok = create_and_save_comparison()
    sys.exit(0 if ok else 1)
