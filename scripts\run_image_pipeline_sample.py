"""
Script tiện ích: chạy pipeline ảnh cho một cặp xe mẫu (chỉ xử lý ảnh, không gọi OpenAI)
"""
import logging
import sys
from config import LOGGING_CONFIG, IMAGE_CONFIG
from image_pipeline import fetch_and_process_images_for_car


def setup_logging():
    logging.basicConfig(
        level=getattr(logging, LOGGING_CONFIG['level']),
        format=LOGGING_CONFIG['format'],
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


def main():
    setup_logging()
    logger = logging.getLogger(__name__)

    if len(sys.argv) < 3:
        print("Usage: python scripts/run_image_pipeline_sample.py <Car1 Name> <Car2 Name>")
        return 1

    car1, car2 = sys.argv[1], sys.argv[2]
    want = IMAGE_CONFIG.get('per_car_images', 5)

    imgs1 = fetch_and_process_images_for_car(car1, want=want)
    imgs2 = fetch_and_process_images_for_car(car2, want=want)

    print(f"Car1: {car1}")
    for img in imgs1:
        print(" ", img.local_path)

    print(f"Car2: {car2}")
    for img in imgs2:
        print(" ", img.local_path)

    print("Done.")
    return 0


if __name__ == "__main__":
    sys.exit(main())

