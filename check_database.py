#!/usr/bin/env python3
"""
Script để kiểm tra dữ liệu trong database
"""
import logging
import sys
from database_manager import DatabaseManager

def setup_logging():
    """Thiết lập logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )

def check_database():
    """Kiểm tra dữ liệu trong database"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    db_manager = DatabaseManager()
    
    try:
        # Kết nối database
        if not db_manager.connect():
            logger.error("Không thể kết nối database")
            return False
        
        # Đếm tổng số xe
        total_cars = db_manager.get_cars_count()
        logger.info(f"Tổng số xe trong database: {total_cars}")
        
        # <PERSON><PERSON><PERSON> một số xe mẫu
        query = "SELECT car_id, car_name, car_price_text, page_number FROM cars LIMIT 10"
        db_manager.cursor.execute(query)
        results = db_manager.cursor.fetchall()
        
        logger.info("10 xe đầu tiên trong database:")
        for i, (car_id, car_name, car_price_text, page_number) in enumerate(results, 1):
            logger.info(f"{i:2d}. {car_name} - {car_price_text} (Trang {page_number})")
        
        # Thống kê theo trang
        query = "SELECT page_number, COUNT(*) FROM cars GROUP BY page_number ORDER BY page_number"
        db_manager.cursor.execute(query)
        page_stats = db_manager.cursor.fetchall()
        
        logger.info("\nThống kê theo trang:")
        for page_num, count in page_stats:
            logger.info(f"Trang {page_num}: {count} xe")
        
        # Thống kê giá xe
        query = """
        SELECT 
            MIN(car_price) as min_price,
            MAX(car_price) as max_price,
            AVG(car_price) as avg_price
        FROM cars 
        WHERE car_price IS NOT NULL
        """
        db_manager.cursor.execute(query)
        price_stats = db_manager.cursor.fetchone()
        
        if price_stats:
            min_price, max_price, avg_price = price_stats
            logger.info(f"\nThống kê giá xe:")
            logger.info(f"Giá thấp nhất: {min_price:,.0f} VND")
            logger.info(f"Giá cao nhất: {max_price:,.0f} VND")
            logger.info(f"Giá trung bình: {avg_price:,.0f} VND")
        
        return True
        
    except Exception as e:
        logger.error(f"Lỗi kiểm tra database: {e}")
        return False
        
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    success = check_database()
    sys.exit(0 if success else 1)
