# Dự án Thu thập Dữ liệu Xe Ô tô từ Bonbanh.com

Dự án này thu thập dữ liệu xe ô tô từ website bonbanh.com và lưu trữ vào cơ sở dữ liệu MySQL.

## Tính năng

- Thu thập dữ liệu xe từ 5 trang đầu tiên của bonbanh.com
- Trích xuất thông tin: tên xe, gi<PERSON> xe, <PERSON> xe, link chi tiết
- Lưu trữ dữ liệu vào MySQL database
- Xử lý lỗi và retry logic
- Rate limiting để tránh quá tải server

## Cài đặt

### 1. <PERSON><PERSON>u cầu hệ thống
- Python 3.7+
- MySQL Server
- Windows (đã test trên Windows)

### 2. Cài đặt dependencies
```bash
pip install -r requirements.txt
```

### 3. Cấu hình database
1. Tạo file `.env` từ `.env.example`:
```bash
copy .env.example .env
```

2. Cập nhật thông tin database trong file `.env`:
```
DB_HOST=localhost
DB_PORT=3306
DB_NAME=xehoi_pro
DB_USER=root
DB_PASSWORD=your_password
```

## Sử dụng

### Chạy scraper chính
```bash
python main.py
```

### Test scraper với 1 trang
```bash
python test_scraper.py
```

### Kiểm tra dữ liệu trong database
```bash
python check_database.py
```

## Cấu trúc dự án

```
xehoi_pro_create_post_6/
├── main.py                 # Script chính
├── car_scraper.py          # Class CarScraper
├── database_manager.py     # Class DatabaseManager
├── config.py              # Cấu hình dự án
├── test_scraper.py        # Script test
├── check_database.py      # Script kiểm tra DB
├── requirements.txt       # Dependencies
├── .env                   # Cấu hình database
├── .env.example          # Template cấu hình
├── README.md             # Hướng dẫn này
└── memory_bank/
    └── wf_bonbanh_scraper.md  # Workflow chi tiết
```

## Kết quả

- **Tổng số xe thu thập**: 100 xe từ 5 trang
- **Số xe lưu vào database**: 99 xe (loại bỏ trùng lặp)
- **Thời gian thực hiện**: ~10 giây
- **Giá xe**: Từ 299 triệu đến 8.6 tỷ VND

## Schema Database

Bảng `cars`:
- `id`: Primary key (AUTO_INCREMENT)
- `car_id`: Mã xe duy nhất (VARCHAR)
- `car_name`: Tên xe (VARCHAR)
- `car_price`: Giá xe dạng số (BIGINT)
- `car_price_text`: Giá xe dạng text (VARCHAR)
- `car_link`: Link chi tiết xe (VARCHAR)
- `page_number`: Số trang scrape (INT)
- `scraped_at`: Thời gian thu thập (TIMESTAMP)

## Lưu ý

- Tuân thủ robots.txt của website
- Có delay 2 giây giữa các request
- Retry tối đa 3 lần khi gặp lỗi
- Sử dụng User-Agent hợp lệ

## Troubleshooting

### Lỗi kết nối database
- Kiểm tra MySQL server đã chạy
- Xác nhận thông tin kết nối trong `.env`
- Đảm bảo database đã được tạo

### Lỗi scraping
- Kiểm tra kết nối internet
- Website có thể thay đổi cấu trúc HTML
- Xem log file `scraper.log` để debug

## License

MIT License
