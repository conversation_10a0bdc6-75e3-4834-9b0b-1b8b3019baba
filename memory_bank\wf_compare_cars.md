# Workflow: <PERSON><PERSON><PERSON> bà<PERSON> viết so sánh xe (compare_cars)

## M<PERSON><PERSON> tiêu
- Chọn ngẫu nhiên 2 xe từ bảng `cars` (xe 2 trong ±20% giá xe 1)
- Gọi OpenAI để sinh bài viết so sánh có cấu trúc
- <PERSON><PERSON><PERSON> kết quả vào bảng `car_comparisons`

## Thành phần
- File `compare_cars.py`: thực thi quy trình
- Mở rộng `database_manager.py`:
  - `create_comparisons_table()`
  - `get_random_car()`
  - `get_random_car_by_price_range(base_price, tolerance, exclude_car_id)`
  - `save_comparison(car1_name, car2_name, content)`
- `config.py`: b<PERSON> sung `LLM_CONFIG`
- `.env.example`: thêm biến môi trường OpenAI

## Các bước chạy
1. Thiết lập env (Windows PowerShell)
   - python -m venv venv
   - .\venv\Scripts\Activate.ps1
   - pip install -r requirements.txt
   - copy .env.example .env
   - Cập nhật OPENAI_API_KEY trong .env
   - pip install openai  # nếu chưa có
2. Chạy script tạo bài viết:
   - python compare_cars.py

## Ghi chú kỹ thuật
- Logging sử dụng cấu hình chung trong `config.LOGGING_CONFIG`
- Gọi OpenAI theo SDK mới (`openai>=1.0`), có fallback chat.completions
- Xử lý lỗi và đảm bảo đóng kết nối DB ở `finally`
- Bảng `car_comparisons` dùng `utf8mb4` để lưu tiếng Việt và Markdown dài (LONGTEXT)

## Mở rộng tương lai
- Lưu thêm metadata (giá, link, id xe)
- Thêm unique hash để tránh trùng cặp xe
- Thêm retry/exponential backoff cho gọi OpenAI

